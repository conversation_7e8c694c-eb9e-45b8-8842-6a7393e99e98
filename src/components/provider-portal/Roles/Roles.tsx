import { ChangeEvent, useState } from "react";
import { useDispatch } from "react-redux";

import {
  Box,
  Button,
  CircularProgress,
  Grid2 as Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";

import CustomInput from "@/common-components/custom-input/custom-input";
import { AlertSeverity } from "@/common-components/snackbar-alert/snackbar-alert";

import { CustomSelect } from "@/components/ui/Form/Select";
import MainDialog from "@/components/ui/MainDialog";
import { ErrorResponseEntity } from "@/models/response/error-response";
import { setSnackbarOn } from "@/redux/actions/snackbar-action";
import { RolePrivilegeUpdateRequest } from "@/sdk/requests";
import { RolesAndPrivilegesControllerService } from "@/sdk/requests/services.gen";
import { GetTenantId } from "@/services/common/get-tenant-id";

import { heading, tableCellCss } from "../../../common-components/table/common-table-widgets";

function formatRoleTitle(role: string): string {
  return role
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

interface Privilege {
  id: number;
  name: string;
  description: string;
  module: string;
  granted: boolean;
}

interface RoleData {
  id: number;
  roleName: string;
  privileges: Privilege[];
}

interface PermissionData {
  id: number;
  name: string;
  description: string;
  module: string;
  roles: Record<string, boolean>;
}

const roleNameMapping: Record<string, string> = {};

const RolesList = () => {
  const dispatch = useDispatch();

  const [updatingCell, setUpdatingCell] = useState<{ permissionId: number; role: string } | null>(null);

  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    permissionId: number | null;
    roleId: number | null;
    granted: boolean;
    role: string;
  }>({ open: false, permissionId: null, roleId: null, granted: false, role: "" });

  const xTenantIdVal = GetTenantId();
  const [searchValue, setSearchValue] = useState("");

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const transformApiData = (apiData: RoleData[]): { permissions: PermissionData[]; roleNames: string[] } => {
    const privilegeMap = new Map<string, PermissionData>();
    const roleNames = apiData.map((role) => roleNameMapping[role.roleName] || role.roleName);

    apiData.forEach((role) => {
      const displayRoleName = roleNameMapping[role.roleName] || role.roleName;
      role.privileges.forEach((privilege) => {
        if (!privilegeMap.has(privilege.name)) {
          privilegeMap.set(privilege.name, {
            id: privilege.id,
            name: privilege.name,
            description: privilege.description,
            module: privilege.module,
            roles: {},
          });
        }
        const permissionData = privilegeMap.get(privilege.name)!;
        permissionData.roles[displayRoleName] = privilege.granted;
      });
    });
    privilegeMap.forEach((permission) => {
      roleNames.forEach((roleName) => {
        if (!(roleName in permission.roles)) {
          permission.roles[roleName] = false;
        }
      });
    });
    return {
      permissions: Array.from(privilegeMap.values()),
      roleNames,
    };
  };

  const {
    data,
    isLoading,
    refetch: refetchRoles,
  } = useQuery({
    queryKey: ["roles-list"],
    queryFn: async () => {
      const response = await RolesAndPrivilegesControllerService.getAllRoles({
        xTenantId: xTenantIdVal,
      });
      return response;
    },
  });

  const transformed = Array.isArray(data?.data) ? transformApiData(data.data) : { permissions: [], roleNames: [] };
  const permissionsData = transformed.permissions;
  const roles = transformed.roleNames;
  const loading = isLoading;

  // Group permissions by module
  let moduleMap = permissionsData.reduce<Record<string, PermissionData[]>>((acc, permission) => {
    if (!acc[permission.module]) {
      acc[permission.module] = [];
    }
    acc[permission.module].push(permission);
    return acc;
  }, {});

  // Filter modules/privileges based on searchValue
  if (searchValue.trim() !== "") {
    const lowerSearch = searchValue.toLowerCase();
    moduleMap = Object.entries(moduleMap).reduce(
      (filtered, [module, perms]) => {
        if (module.toLowerCase().includes(lowerSearch)) {
          filtered[module] = perms;
        } else {
          const filteredPerms = perms.filter(
            (perm) =>
              perm.name.toLowerCase().includes(lowerSearch) || perm.description?.toLowerCase().includes(lowerSearch)
          );
          if (filteredPerms.length > 0) {
            filtered[module] = filteredPerms;
          }
        }
        return filtered;
      },
      {} as Record<string, PermissionData[]>
    );
  }

  const roleMetaMap: Record<string, { id: number; roleName: string }> = {};
  if (Array.isArray(data?.data)) {
    data.data.forEach((role) => {
      const displayRoleName = roleNameMapping[role.roleName] || role.roleName;
      roleMetaMap[displayRoleName] = { id: role.id, roleName: role.roleName };
    });
  }

  // Helper to determine access level for a module/role
  function getAccessLevel(modulePerms: PermissionData[], role: string) {
    const grantedCount = modulePerms.filter((p) => p.roles[role]).length;
    if (grantedCount === 0) return "none";
    if (grantedCount === modulePerms.length) return "full";
    return "read";
  }

  // Add mutation for updating role permissions
  const updateRolePermissionMutation = useMutation({
    mutationFn: (data: { requestBody: RolePrivilegeUpdateRequest[]; xTenantId: string }) =>
      RolesAndPrivilegesControllerService.updateRolePermission({
        requestBody: data.requestBody,
        xTenantId: data.xTenantId,
      }),
    onSuccess: (response) => {
      const message = (response as unknown as AxiosResponse)?.data?.message || "Permissions updated successfully!";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetchRoles();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error?.body?.message || "Failed to update permissions",
        })
      );
      refetchRoles();
    },
  });

  // Helper to update access level for a module/role
  async function handleAccessLevelChange(module: string, role: string, access: string) {
    const perms = moduleMap[module];
    const roleMeta = roleMetaMap[role];
    if (!perms || !roleMeta) return;

    // Only set the specific cell as updating
    setUpdatingCell({ permissionId: perms[0].id, role });

    // For demo: assuming first permission is 'read', rest are 'full'. Adjust as needed.
    let updates: { permissionId: number; granted: boolean }[] = [];
    if (access === "none") {
      updates = perms.map((p) => ({ permissionId: p.id, granted: false }));
    } else if (access === "read") {
      updates = perms.map((p) => ({
        permissionId: p.id,
        granted: p.name.toUpperCase().includes("VIEW"),
      }));
    } else {
      updates = perms.map((p) => ({ permissionId: p.id, granted: true }));
    }

    // Create payload for all updates in a single request
    const requestPayload = updates.map((update) => ({
      roleId: roleMeta.id,
      privilegeId: update.permissionId,
      granted: update.granted,
    }));

    // Update the UI optimistically
    const modulePerms = moduleMap[module];
    if (access === "none") {
      modulePerms.forEach((p) => (p.roles[role] = false));
    } else if (access === "read") {
      modulePerms.forEach((p) => (p.roles[role] = p.name.toUpperCase().includes("VIEW")));
    } else {
      modulePerms.forEach((p) => (p.roles[role] = true));
    }

    // Call the mutation
    await updateRolePermissionMutation.mutateAsync({
      requestBody: requestPayload,
      xTenantId: xTenantIdVal,
    });

    // Clear loading state
    setUpdatingCell(null);
  }

  const handleConfirmChange = async () => {
    if (confirmDialog.permissionId && confirmDialog.roleId) {
      setUpdatingCell({ permissionId: confirmDialog.permissionId, role: confirmDialog.role });
      setConfirmDialog((prev) => ({ ...prev, open: false }));

      // Update the UI optimistically
      if (confirmDialog.permissionId) {
        // Find the permission in moduleMap and update it
        Object.values(moduleMap).forEach((perms) => {
          const perm = perms.find((p) => p.id === confirmDialog.permissionId);
          if (perm) {
            perm.roles[confirmDialog.role] = !confirmDialog.granted;
          }
        });
      }

      await updateRolePermissionMutation.mutateAsync({
        requestBody: [
          {
            roleId: confirmDialog.roleId,
            privilegeId: confirmDialog.permissionId,
            granted: !confirmDialog.granted,
          },
        ],
        xTenantId: xTenantIdVal,
      });

      setUpdatingCell(null);
    }
  };

  const handleCancelChange = () => {
    setConfirmDialog((prev) => ({ ...prev, open: false }));
  };

  const resetRolePermissionMutation = useMutation({
    mutationFn: () =>
      RolesAndPrivilegesControllerService.resetAllRolePrivileges({
        xTenantId: xTenantIdVal,
      }),
    onSuccess: () => {
      const message = "Permissions reset successfully";
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.SUCCESS,
          message,
        })
      );
      refetchRoles();
    },
    onError: (error: ErrorResponseEntity) => {
      dispatch(
        setSnackbarOn({
          severity: AlertSeverity.ERROR,
          message: error?.body?.message || "Failed to reset permissions",
        })
      );
    },
  });

  if (loading) {
    return (
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        justifyItems="center"
        sx={{ minHeight: 400, width: "100%" }}
      >
        <Grid>
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            minHeight="300px"
            paddingTop={20}
          >
            <CircularProgress size={40} />
          </Box>
        </Grid>
      </Grid>
    );
  }

  const handleReset = async () => {
    await resetRolePermissionMutation.mutateAsync();
  };

  return (
    <Grid
      sx={{
        border: "1px solid #E0E0E0",
        borderRadius: "5px",
        overflow: "visible",
      }}
    >
      <Grid container direction="column">
        <Grid container alignItems="center" justifyContent="space-between" mb={0} p={2}>
          <Grid>
            <Typography variant="h6">Roles and Privileges</Typography>
          </Grid>
          <Grid container alignItems="center" justifyContent="space-between" spacing={2}>
            <Grid sx={{ width: "300px", marginTop: "6px" }}>
              <CustomInput
                value={searchValue}
                onChange={handleSearchChange}
                placeholder="Search Module"
                name="search"
              />
            </Grid>
            <Grid>
              <Button
                variant="contained"
                onClick={() => {
                  handleReset();
                }}
              >
                Reset
              </Button>
            </Grid>
          </Grid>
        </Grid>

        <Grid sx={{ width: "100%" }}>
          <TableContainer sx={{ maxHeight: "75vh", overflow: "auto", overflowX: "auto" }}>
            <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      ...heading,
                      minWidth: "150px",
                      padding: "8px",
                    }}
                    align="left"
                  >
                    <Typography variant="bodySmall" fontWeight={550}>
                      Module
                    </Typography>
                  </TableCell>
                  {roles.map((role, index) => (
                    <TableCell
                      key={index}
                      sx={{
                        ...heading,
                        minWidth: "120px",
                        padding: "8px",
                      }}
                      align="center"
                    >
                      <Typography variant="bodySmall" fontWeight={550}>
                        {formatRoleTitle(role)}
                      </Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={roles.length + 2} align="center">
                      <Typography variant="bodySmall" fontWeight={550}>
                        Loading...
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : Object.keys(moduleMap).length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={roles.length + 1} align="center">
                      <Typography>No data found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  Object.keys(moduleMap).map((module) => (
                    <TableRow key={module} hover>
                      <TableCell
                        sx={{
                          ...heading,
                          padding: "8px",
                        }}
                        align="left"
                      >
                        <Typography variant="bodySmall">{module}</Typography>
                      </TableCell>
                      {roles.map((role, roleIndex) => {
                        const access = getAccessLevel(moduleMap[module], role);
                        const isUpdating =
                          updatingCell &&
                          updatingCell.role === role &&
                          moduleMap[module].some((p) => p.id === updatingCell.permissionId);
                        return (
                          <TableCell
                            align="center"
                            key={roleIndex}
                            sx={{
                              ...heading,
                              padding: "4px",
                            }}
                            height={"40px"}
                          >
                            {isUpdating ? (
                              <CircularProgress size={20} />
                            ) : (
                              <Grid alignItems={"center"} px={"10px"}>
                                <Grid>
                                  <CustomSelect
                                    options={[
                                      { label: "No Access", value: "none" },
                                      { label: "Read Access", value: "read" },
                                      { label: "Full Access", value: "full" },
                                    ]}
                                    value={access}
                                    onChange={(val) => handleAccessLevelChange(module, role, val)}
                                    noLabel
                                  />
                                </Grid>
                              </Grid>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>

      {/* Confirmation Dialog */}
      <MainDialog
        title="Confirm Permission Change"
        open={confirmDialog.open}
        handleClose={handleCancelChange}
        maxWidth="sm"
        actionButtons={
          <>
            <Button onClick={handleCancelChange} variant={"outlined"} color={"error"}>
              Cancel
            </Button>
            <Button onClick={handleConfirmChange} variant={"contained"} color={"primary"}>
              {confirmDialog.granted ? "Remove" : "Allow"}
            </Button>
          </>
        }
      >
        <Typography variant="body1">
          Are you sure you want to {confirmDialog.granted ? "remove" : "grant"} this permission for the role{" "}
          <b>{formatRoleTitle(confirmDialog.role)}</b>?
        </Typography>
      </MainDialog>
    </Grid>
  );
};

export default RolesList;
