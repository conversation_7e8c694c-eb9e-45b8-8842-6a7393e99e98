import React, { ChangeEvent, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import ArchiveOutlinedIcon from "@mui/icons-material/ArchiveOutlined";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import RestoreIcon from "@mui/icons-material/Restore";
import {
  Button,
  IconButton,
  Link,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Grid } from "@mui/system";
import { DataGrid, GridColDef, GridRowId, GridRowParams, GridRowSelectionModel } from "@mui/x-data-grid";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { format, isValid, parseISO } from "date-fns";

import ConfirmationPopUp from "@/common-components/confirmation-pop-up/confirmation-pop-up";
import CustomDialog from "@/common-components/custom-dialog/custom-dialog";
import CustomDrawer from "@/common-components/custom-drawer/custom-drawer";
import CustomInput from "@/common-components/custom-input/custom-input";
import CustomSelect from "@/common-components/custom-select/customSelect";
import Paginator from "@/common-components/paginator/paginator";
import { heading, iconStyles, tableCellCss, typographyCss } from "@/common-components/table/common-table-widgets";
import { TableHeaders } from "@/common-components/table/table-models";

import { useDrawer } from "@/components/providers/DrawerProvider";
import useAuthority from "@/hooks/use-authority";
import useApiFeedback from "@/hooks/useApiFeedback";
import { ProviderRole } from "@/models/provider/provider-modal";
import { ContentObject } from "@/models/response/response-content-entity";
import { setIsLoading } from "@/redux/actions/loader-action";
import { RootState } from "@/redux/store";
import { usePatientControllerServiceUpdatePatientArchiveStatus } from "@/sdk/queries";
import {
  Address,
  EmergencyContact,
  PatientControllerService,
  Provider,
  ProviderControllerService,
} from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { formatDateNewFormat } from "@/utils/format/date";
import { theme } from "@/utils/theme";

import AssignCarePlan from "./assign-care-plan";
import EhrPatientEnroll from "./enroll-Ehr-patient/ehrPatient-enroll-dialog";
import InvitePatientForm from "./invite-patient-form";
import UploadCSVFile from "./upload-csv-dialog";

export const mockHeaders: TableHeaders[] = [
  // { header: "Sr No", minWidth: "100px" },
  { header: "Name" },
  { header: "MRN" },
  { header: "Date of Birth" },
  { header: "Phone Number" },
  { header: "Reading" },
  { header: "Monitoring" },
  { header: "Home Visit" },
  { header: "Tele Visit" },
  { header: "Alert" },
  { header: "Last Sync" },
  { header: "Actions" },
];

const PatientsList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  // const roles = useAuthority();

  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const [selectPatient, setSelectedPatient] = useState<Patient | null>();
  const [openConfirmRestorePopUp, setOpenConfirmRestorePopUp] = useState(false);
  const [openConfirmDeletePopUp, setOpenConfirmDeletePopUp] = useState(false);
  const [openCSVDialog, setOpenCSVDialog] = useState(false);

  const [searchByName, setSearchByName] = useState("");
  const [searchByMRN, setSearchByMRN] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const xtenantId = GetTenantId();
  const [page, setPage] = useState(0);
  const [size, setSize] = useState(10);

  const [providerOptions, setProviderOptions] = useState<{ label: string; value: string }[]>([]);
  const [searchProviderValue, setSearchProviderValue] = useState("");

  const [sortBy, setSortBy] = useState("modified");
  const [sortDirection, setSortDirection] = useState("desc");
  const [rows, setRows] = React.useState<Patient[]>([]);
  const [totalPages, setTotalPages] = React.useState(0);
  const [totalElements, setTotalElements] = useState<number>(0);
  totalElements;
  const role = useAuthority();
  const { data: ProviderData } = useSelector((state: RootState) => state.providerProfileReducer);

  const [assignCarePlanDrawer, setAssignCarePlanDrawer] = useState(false);
  const [selectedCheckboxPatient, setSelectedCheckboxPatient] = useState<GridRowId[]>([]);
  const [selectionModel, setSelectionModel] = useState<Set<string>>(new Set());
  const handleSelectedPatientData = (newSelectionModel: GridRowSelectionModel) => {
    const currentPageIds = listRowData.map((row) => row.id);
    const newSelection = new Set(selectionModel);

    currentPageIds.forEach((id) => {
      const stringId = id.toString();
      const isCurrentlySelected = newSelectionModel.includes(id);
      const wasSelectedBefore = newSelection.has(stringId);

      if (isCurrentlySelected && !wasSelectedBefore) {
        newSelection.add(stringId);
      } else if (!isCurrentlySelected && wasSelectedBefore) {
        newSelection.delete(stringId);
      }
    });

    setSelectionModel(newSelection);
    setSelectedCheckboxPatient(Array.from(newSelection));
  };

  const { data, isLoading, isSuccess, refetch } = useQuery({
    enabled: !!xtenantId,
    queryKey: [
      "list-of-patients",
      page,
      size,
      xtenantId,
      searchByMRN,
      searchByName,
      selectedStatus,
      sortBy,
      sortDirection,
      ProviderData,
    ],
    queryFn: () =>
      PatientControllerService.getAllPatient({
        page,
        size,
        sortBy,
        sortDirection,
        status: selectedStatus === "active" ? true : selectedStatus === "inactive" ? false : undefined,
        archive: selectedStatus === "archived" ? true : undefined,
        xTenantId: xtenantId,
        mrn: searchByMRN,
        name: searchByName,
        providerId: role.isProvider ? ProviderData?.uuid : "",
      }),
  });

  useEffect(() => {
    if (isSuccess) {
      const response = (data as unknown as AxiosResponse).data as ContentObject<Patient[]>;

      const patientData = response?.content;
      setTotalPages(response?.page?.totalPages as number);
      setTotalElements(response?.page?.totalElements as number);

      const tablePayload = patientData?.map((patient) => {
        return {
          uuid: patient?.uuid,
          firstName: `${patient.firstName} ` || "",
          lastName: `${patient.lastName}` || "",
          mrn: patient.mrn || "",
          birthDate: patient.birthDate || "-",
          mobileNumber: patient.phone ? patient.phone.replace(/-/g, "") : "",
          address: {
            line1: patient?.address?.line1,
            line2: patient?.address?.line2,
            city: patient?.address?.city,
            state: patient?.address?.state,
            country: patient?.address?.country,
            zipcode: patient?.address?.zipcode,
          },
          active: patient?.active,
          email: patient?.email,
          archive: patient.archive,
          schemaType: patient.schemaType,
          nurseId: patient.nurseId,
          providerId: patient.providerId,
          lastSync: patient.lastSync,
          carePlanAssigned: patient.carePlanAssigned ?? false,
          alerts: patient?.alerts,
        } as Patient;
      });

      setRows(tablePayload);
    }
  }, [data, isSuccess]);

  const handleOnClickViewLink = (link: string) => {
    link;
  };

  handleOnClickViewLink("");

  useEffect(() => {
    dispatch(setIsLoading(isLoading));
  }, [dispatch, isLoading]);

  const handleRecordsPerPageChange = (recordsPerPage: number) => {
    setPage(0);
    setSize(recordsPerPage);
  };

  const handlePageChange = (event: ChangeEvent<unknown> | null, page: number) => {
    event;
    setPage(page);
  };

  // Archive
  const xTenantId = GetTenantId();
  const {
    mutateAsync: mutateAsyncArchive,
    isError: isErrorArchivePatient,
    error: errorArchivePatient,
    isSuccess: successPatient,
    data: dataArchivePatient,
    // isPending: isPendingPatient
  } = usePatientControllerServiceUpdatePatientArchiveStatus();

  useApiFeedback(
    isErrorArchivePatient,
    errorArchivePatient,
    successPatient,
    (dataArchivePatient?.message || "User archive status updated!") as string
  );

  const handlePatientArchive = async () => {
    await mutateAsyncArchive({
      patientId: selectPatient?.uuid || selectPatient?.id || "",
      status: true,
      xTenantId: xTenantId,
    });
    setOpenConfirmDeletePopUp(false);
    refetch();
  };

  const handlePatientRestore = async () => {
    await mutateAsyncArchive({
      patientId: selectPatient?.uuid || selectPatient?.id || "",
      status: false,
      xTenantId: xTenantId,
    });
    setOpenConfirmRestorePopUp(false);
    refetch();
  };

  const handleSorting = (header: string) => {
    if (header == "Name") {
      setSortBy("firstName");
    } else if (header === "Email") {
      setSortBy("email");
    } else if (header === "Status") {
      setSortBy("active");
    }

    setSortDirection((prev) => (prev === "desc" ? "asc" : "desc"));
  };

  //Provider Api

  const {
    data: providers,
    isPending: isLoadingProviders,
    isSuccess: isSuccessProviders,
  } = useQuery({
    queryKey: ["providers", searchProviderValue],
    queryFn: async () => {
      const res = await ProviderControllerService.getAllProviders({
        xTenantId,
        page: 0,
        size: 100,
        sortBy: "modified",
        sortDirection: "desc",
        role: ProviderRole.PROVIDER,
        status: true,
        archive: false,
        searchString: searchProviderValue,
      });

      const { data: providerData } = res as AxiosResponse<ContentObject<Provider[]>>;
      const options = providerData?.content.map((item) => ({
        label: `${item.firstName} ${item.lastName}`,
        value: String(item.uuid),
      }));

      return options;
    },
  });

  useEffect(() => {
    if (isSuccessProviders) {
      setProviderOptions(providers || []);
    }
  }, [providers, isSuccessProviders]);

  // nurse option

  const handleDrawer = {
    invitePatientForm: (action: string, patient?: Patient) => {
      openDrawer(
        {
          title: `${action} Patient`,
          component: (
            <InvitePatientForm
              handleCloseDrawer={closeDrawer}
              patientUuid={patient?.id}
              xTenantId={xTenantId}
              isLoadingProviders={isLoadingProviders}
              providerOptions={providerOptions}
              setSearchProviderValue={setSearchProviderValue}
            />
          ),
        },
        "80%"
      );
    },
    enrollFromEhr: () => {
      openDrawer(
        {
          title: "Enroll from EHR",
          component: (
            <EhrPatientEnroll
              handleCloseDrawer={closeDrawer}
              isLoadingProviders={isLoadingProviders}
              providerOptions={providerOptions}
              setSearchProviderValue={setSearchProviderValue}
              refetch={refetch}
            />
          ),
        },
        "60%"
      );
    },
    assignCarePlan: () => {
      openDrawer(
        {
          title: "Assign Care Plan",
          component: (
            <AssignCarePlan selectedCheckboxPatient={selectedCheckboxPatient} handleCloseDrawer={closeDrawer} />
          ),
        },
        "60%"
      );
    },
  };

  // Column
  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Name",
      hideSortIcons: false,

      renderCell: (params) => {
        const patient = params.row;

        return (
          <TableCell sx={{ ...heading, width: "100%" }} align="center">
            <Grid container flexDirection="column" ml={2}>
              <Grid container flexDirection="column" sx={{ cursor: "pointer" }}>
                <Typography
                  fontWeight={550}
                  color="primary"
                  variant="bodySmall"
                  onClick={() => navigate(`${patient.id}/profile`)}
                >
                  {patient.name ? `${patient.name ?? "-"}` : "-"}
                </Typography>
              </Grid>
            </Grid>
          </TableCell>
        );
      },
      minWidth: 200,
    },
    {
      field: "mrn",
      headerName: "MRN",
      renderCell: (params) => {
        const patient = params.row;
        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column" ml={4}>
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.mrn || "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 200,
    },
    {
      field: "dateofBirth",
      headerName: "Date Of Birth",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row;
        return (
          <TableCell sx={{ ...heading }} align="center">
            <Grid container flexDirection="column">
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.birthDate
                  ? (() => {
                      const parsedDate = parseISO(patient.birthDate);
                      return isValid(parsedDate) ? format(parsedDate, "MM/dd/yyyy") : "-";
                    })()
                  : "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      minWidth: 130,
    },
    {
      field: "phonenumber",
      headerName: "Phone Number",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row;
        return (
          <TableCell sx={{ ...heading }} align="center">
            <Grid container flexDirection="column" ml={2}>
              <Typography sx={typographyCss} variant="bodySmall">
                {patient?.phonenumber
                  ? `${patient.phonenumber.replace(/^\+1(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3").replace(/^(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3")}`
                  : "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 200,
    },
    {
      field: "reading",
      headerName: "Reading",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row;
        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column">
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.reading ?? "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 100,
    },
    {
      field: "monitoring",
      headerName: "Monitoring",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row as Patient;
        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column">
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.monitoring ?? "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 100,
    },
    {
      field: "homeVisit",
      headerName: "Home Visit",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row as Patient;
        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column">
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.homeVisit ?? "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 100,
    },
    {
      field: "teleVisit",
      headerName: "Tele Visit",
      sortable: false,
      renderCell: () => (
        <TableCell sx={{ ...heading }} align="left">
          <Grid container flexDirection="column">
            <Typography sx={typographyCss} variant="bodySmall">
              {"0"}
            </Typography>
          </Grid>
        </TableCell>
      ),
      width: 100,
    },
    {
      field: "alerts",
      headerName: "Alerts",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row as Patient;
        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column">
              <Typography sx={typographyCss} variant="bodySmall">
                {patient.alerts ?? "-"}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      width: 100,
    },
    {
      field: "lastSync",
      headerName: "Last Sync",
      sortable: false,
      renderCell: (params) => {
        const patient = params.row;

        return (
          <TableCell sx={{ ...heading }} align="left">
            <Grid container flexDirection="column" ml={2}>
              <Typography sx={typographyCss} variant="bodySmall">
                {formatDateNewFormat(patient.lastSync)}
              </Typography>
            </Grid>
          </TableCell>
        );
      },
      minWidth: 250,
    },
    {
      field: "action",
      headerName: "Action",
      minWidth: 200,
      sortable: false,
      renderCell: (params) => {
        const patient = params.row as Patient;
        return (
          <TableCell sx={{ ...heading }}>
            <Grid container columnGap={1.2} flexWrap="nowrap" ml={5}>
              <IconButton
                sx={{ padding: "0px 5px" }}
                aria-label="edit"
                onClick={() => handleDrawer.invitePatientForm("Edit", patient)}
              >
                <EditOutlinedIcon sx={iconStyles} />
              </IconButton>
              {!patient.archive ? (
                <IconButton
                  aria-label="delete"
                  onClick={() => {
                    setSelectedPatient({
                      ...patient,
                      name: patient.name || `${patient.firstName || ""} ${patient.lastName || ""}`,
                      email: patient.email || "-",
                      mrn: patient.mrn || "-",
                      archive: patient.archive,
                    });
                    setOpenConfirmDeletePopUp(true);
                  }}
                  sx={{ padding: "0px" }}
                >
                  <ArchiveOutlinedIcon sx={iconStyles} />
                </IconButton>
              ) : (
                <IconButton
                  aria-label="restore"
                  onClick={() => {
                    setSelectedPatient({
                      ...patient,
                      name: patient.name || `${patient.firstName || ""} ${patient.lastName || ""}`,
                      email: patient.email || "-",
                      mrn: patient.mrn || "-",
                      archive: patient.archive,
                    });
                    setOpenConfirmRestorePopUp(true);
                  }}
                  sx={{ padding: "0px" }}
                >
                  <RestoreIcon sx={iconStyles} />
                </IconButton>
              )}
            </Grid>
          </TableCell>
        );
      },
    },
  ];
  const listRowData = rows.map((patient) => ({
    id: patient.uuid || "-",
    name: `${patient.firstName || "-"} ${patient.lastName || ""}`,
    mrn: patient.mrn || "-",
    birthDate: patient.birthDate || "-",
    phonenumber: patient?.mobileNumber
      ? `${patient.mobileNumber.replace(/^\+1(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3").replace(/^(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3")}`
      : "-",
    reading: patient.reading ?? "-",
    monitoring: patient.monitoring ?? "-",
    homeVisit: patient.homeVisit ?? "-",
    teleVisit: patient.teleVisit ?? "-",
    alerts: patient.alerts ?? "-",
    lastSync: patient.lastSync || "-",
    action: patient.archive ?? "",
    carePlanAssigned: patient.carePlanAssigned ?? false,
    archive: patient.archive ?? false,
  }));

  // Reset selection when search filters change
  useEffect(() => {
    setSelectionModel(new Set());
    setSelectedCheckboxPatient([]);
  }, [searchByName, searchByMRN, selectedStatus]);

  return (
    <Grid height={"auto"} p={2} width={"100%"} maxWidth={"100%"} overflow={"auto"}>
      <Grid
        border={`1px solid ${theme.palette.grey[300]}`}
        boxShadow={`0px 0px 16px 0px #021D2614`}
        height={"100%"}
        borderRadius={"8px"}
        container
        flexDirection={"column"}
      >
        <Grid container p={2} justifyContent={"space-between"} rowGap={2}>
          <Grid container alignItems={"center"} columnGap={2} rowGap={2}>
            <Typography variant="bodyMedium" fontWeight={550} mr={2}>
              Patients
            </Typography>
            <Grid>
              <CustomInput
                placeholder={"Search by name"}
                name={"searchByName"}
                value={searchByName}
                onChange={(e) => setSearchByName(e.target.value)}
                onDebounceCall={(searchbyname) => {
                  setSearchByName(searchbyname);
                }}
                onInputEmpty={() => setSearchByName("")}
              />
            </Grid>
            <Grid>
              {/* <CustomLabel label="Search by MRN" /> */}
              <CustomInput
                placeholder={"Search by MRN"}
                name={"searchByName"}
                value={searchByMRN}
                onChange={(e) => setSearchByMRN(e.target.value)}
                onDebounceCall={(searchbymrn) => {
                  setSearchByMRN(searchbymrn);
                }}
                onInputEmpty={() => setSearchByMRN("")}
              />
            </Grid>
            <Grid container width={"200px"}>
              {/* <CustomLabel label="Select status" /> */}
              <CustomSelect
                placeholder={"Select status"}
                name={"searchByMRN"}
                value={selectedStatus}
                enableDeselect
                items={[
                  { value: "active", label: "Active" },
                  { value: "inactive", label: "Inactive" },
                  { value: "archived", label: "Archived" },
                ]}
                onChange={function (e: SelectChangeEvent<string>): void {
                  setSelectedStatus(e.target.value);
                }}
              />
            </Grid>
          </Grid>
          <Grid container direction="row" alignItems="center" columnGap={2} rowGap={2}></Grid>
          <Grid container direction="row" alignItems="center" columnGap={2} rowGap={2}>
            {!role.isProvider && (
              <>
                <Button variant="outlined" onClick={() => handleDrawer.enrollFromEhr()}>
                  <Typography variant="bodySmall">Enroll from EHR</Typography>
                </Button>
                <Button variant="outlined" onClick={() => setOpenCSVDialog(true)}>
                  <Typography variant="bodySmall">Import CSV</Typography>
                </Button>
                {!role.isProvider || role.isSiteAdmin ? (
                  <Button variant="contained" onClick={() => handleDrawer.invitePatientForm("Invite")}>
                    <Typography variant="bodySmall">Invite patient</Typography>
                  </Button>
                ) : null}
              </>
            )}

            {(role.isProvider || role.isProviderPortal || role.isSiteAdmin) && (
              <Button
                variant="outlined"
                disabled={selectedCheckboxPatient.length == 0}
                onClick={() => handleDrawer.assignCarePlan()}
              >
                Assign Care Plan
              </Button>
            )}
          </Grid>
        </Grid>
        <Grid width={"100%"}>
          {role.isProvider || role.isSiteAdmin || role.isProviderPortal ? (
            <DataGrid
              isRowSelectable={(params: GridRowParams) => !params.row.carePlanAssigned}
              rows={listRowData}
              columns={columns}
              checkboxSelection
              keepNonExistentRowsSelected
              rowSelectionModel={Array.from(selectionModel)}
              onRowSelectionModelChange={handleSelectedPatientData}
              pagination
              paginationModel={{ page, pageSize: size }}
              onPaginationModelChange={(model) => {
                // Remember to preserve selections when changing pages
                setPage(model.page);
                setSize(model.pageSize);
              }}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 10,
                  },
                },
              }}
              pageSizeOptions={[10, 15, 20, 25]}
              rowCount={totalElements}
              paginationMode="server"
              autoHeight={false}
              sx={{
                border: 0,
                // height: "650px",
                "& .MuiDataGrid-virtualScroller": {
                  overflowY: "auto",
                  "&::-webkit-scrollbar": {
                    width: "8px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "#bdbdbd",
                    borderRadius: "4px",
                  },
                  "&::-webkit-scrollbar-track": {
                    backgroundColor: "#f5f5f5",
                  },
                },
                "& .MuiDataGrid-columnHeader": {
                  "& .MuiDataGrid-sortIcon": {
                    opacity: 1,
                  },
                  "& .MuiDataGrid-columnHeaderTitleContainer": {
                    display: "flex",
                    justifyContent: "center",
                  },
                },
                "& .MuiDataGrid-cell:focus": {
                  outline: "1px solid transparent",
                },
                "& .MuiDataGrid-cell:focus-within": {
                  outline: "none",
                },
                "& .MuiDataGrid-row:focus": {
                  outline: "none",
                },
                "& .MuiDataGrid-row:focus-within": {
                  outline: "none",
                },
              }}
              disableColumnMenu={true}
              disableColumnResize={true}
            />
          ) : (
            <TableContainer sx={{ maxHeight: "78vh", overflow: "auto" }}>
              <Table stickyHeader aria-label="sticky table" sx={tableCellCss}>
                <TableHead>
                  <TableRow>
                    {mockHeaders.map((header, index) => (
                      <TableCell
                        sx={{
                          ...heading,
                          minWidth: header.minWidth ? header.minWidth : "inherit",
                          maxWidth: header.maxWidth ? header.maxWidth : "inherit",
                        }}
                        align="left"
                        key={index}
                      >
                        {header.header === "Name" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleSorting(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortDirection == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : header.header === "Status" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleSorting(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortDirection == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : header.header === "Email" ? (
                          <Link
                            style={{
                              color: "#667085",
                              textDecoration: "none",
                              cursor: "pointer",
                            }}
                            onClick={() => handleSorting(header.header)}
                          >
                            <Typography fontWeight={550} variant="bodySmall" display={"flex"} gap={0.5}>
                              {header.header}
                              <Typography mt={0.3}>
                                {sortDirection == "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                )}
                              </Typography>
                            </Typography>
                          </Link>
                        ) : (
                          <Grid
                            container
                            flexDirection={"column"}
                            alignContent={header.header === "Actions" ? `center` : "flex-start"}
                          >
                            <Typography variant="bodySmall">{header.header}</Typography>
                          </Grid>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rows.length > 0 ? (
                    rows.map((patient, index) => (
                      <TableRow key={index}>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {page * size + index + 1}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Grid container flexDirection={"column"} sx={{ cursor: "pointer" }}>
                              <Typography
                                fontWeight={550}
                                color="primary"
                                variant="bodySmall"
                                onClick={() => navigate(`${patient.uuid}/profile`)}
                              >
                                {patient.name ? `${patient.name} ` : ""}
                              </Typography>
                            </Grid>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {patient.mrn || "-"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {patient.birthDate
                                ? (() => {
                                    const parsedDate = parseISO(patient.birthDate);
                                    return isValid(parsedDate) ? format(parsedDate, "MM/dd/yyyy") : "-";
                                  })()
                                : "-"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {patient?.mobileNumber
                                ? `${patient.mobileNumber.replace(/^\+1(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3").replace(/^(\d{3})(\d{3})(\d{4})$/, "+1$1-$2-$3")}`
                                : "-"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"25px"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {"0"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"30px"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {"0"}
                            </Typography>
                          </Grid>
                        </TableCell>

                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"30px"}>
                            {"-"}
                          </Grid>
                        </TableCell>

                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"12px"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {"0"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"30px"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {"-"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }} align="left">
                          <Grid container flexDirection={"column"} ml={"30px"}>
                            <Typography sx={typographyCss} variant="bodySmall">
                              {"-"}
                            </Typography>
                          </Grid>
                        </TableCell>
                        <TableCell sx={{ ...heading }}>
                          <Grid container pl={3} columnGap={1.2} flexWrap={"nowrap"} ml={"30px"}>
                            <IconButton
                              sx={{ padding: "0px 5px" }}
                              aria-label="edit"
                              onClick={() => handleDrawer.invitePatientForm("Edit", patient)}
                            >
                              <EditOutlinedIcon sx={iconStyles} />
                            </IconButton>

                            {!patient.archive ? (
                              <IconButton
                                aria-label="delete"
                                onClick={() => {
                                  console.log("patient", patient);
                                  setSelectedPatient({
                                    ...patient,
                                    name: patient.name || `${patient.firstName || ""} ${patient.lastName || ""}`,
                                    email: patient.email || "-",
                                    mrn: patient.mrn || "-",
                                    archive: patient.archive,
                                  });
                                  setOpenConfirmDeletePopUp(true);
                                }}
                                sx={{ padding: "0px" }}
                              >
                                <ArchiveOutlinedIcon sx={iconStyles} />
                              </IconButton>
                            ) : (
                              <IconButton
                                aria-label="delete"
                                onClick={() => {
                                  setSelectedPatient({
                                    ...patient,
                                    name: patient.name || `${patient.firstName || ""} ${patient.lastName || ""}`,
                                    email: patient.email || "-",
                                    mrn: patient.mrn || "-",
                                    archive: patient.archive,
                                  });
                                  setOpenConfirmRestorePopUp(true);
                                }}
                                sx={{ padding: "0px" }}
                              >
                                <RestoreIcon sx={iconStyles} />
                              </IconButton>
                            )}
                          </Grid>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={12} align="center">
                        <Typography variant="bodySmall" fontWeight={550}>
                          No records found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Grid>
        {!(role.isProvider || role.isSiteAdmin || role.isProviderPortal) && (
          <Grid container>
            <Paginator
              page={page}
              totalPages={totalPages}
              totalRecord={totalElements}
              onRecordsPerPageChange={handleRecordsPerPageChange}
              onPageChange={handlePageChange}
            />
          </Grid>
        )}

        <CustomDrawer
          anchor="right"
          open={assignCarePlanDrawer}
          onClose={() => setAssignCarePlanDrawer(false)}
          title={"Assign Care Plan"}
        />

        <ConfirmationPopUp
          title={`Restore Item`}
          confirmButtonName="Restore"
          subtitle={"Are you sure you want to restore the following Patient?"}
          open={openConfirmRestorePopUp}
          onClose={() => setOpenConfirmRestorePopUp(false)}
          onConfirm={() => handlePatientRestore()}
          message={`Do you really want to restore `}
          rowData={[selectPatient?.name || "", selectPatient?.mrn || "-"]}
          header={[{ header: "Name" }, { header: "MRN" }]}
        />

        <ConfirmationPopUp
          open={openConfirmDeletePopUp}
          confirmButtonName="Archive"
          rowData={[selectPatient?.name || "", selectPatient?.mrn || "-"]}
          header={[{ header: "Name" }, { header: "MRN" }]}
          title={`Archive Item`}
          subtitle={"Are you sure you want to archive the following Patient?"}
          onClose={() => setOpenConfirmDeletePopUp(false)}
          onConfirm={() => handlePatientArchive()}
          message={`Do you really want to archive this user`}
        />
      </Grid>

      <CustomDialog
        buttonName={["Invite"]}
        onClose={() => setOpenCSVDialog(false)}
        open={openCSVDialog}
        title="Import Patient"
      >
        <UploadCSVFile onClose={() => setOpenCSVDialog(false)} refetch={refetch} />
      </CustomDialog>
    </Grid>
  );
};

export default PatientsList;

type Patient = {
  id?: string;
  phone: string | undefined;
  uuid?: string;
  firstName: string;
  lastName: string;
  name?: string;
  email: string;
  mobileNumber?: string;
  gender?: "MALE" | "FEMALE" | "OTHER";
  middleName?: string;
  mrn?: string;
  birthDate?: string;
  avatar?: string;
  providerId: {
    [key: string]: string;
  };
  nurseId: {
    [key: string]: string;
  };
  schemaType: "INTERNAL" | "EXTERNAL";
  address?: Address;
  signature?: string;
  signatureChanged?: boolean;
  emergencyContact?: EmergencyContact;
  consentFormSigned?: boolean;
  acceptTerms?: boolean;
  source?: string;
  providerNpi?: string;
  nurseNpi?: string;
  nurseAvatar?: string;
  emailVerified?: boolean;
  active?: boolean;
  archive?: boolean;
  reading: string;
  monitoring: string;
  teleVisit: string;
  homeVisit: string;
  lastSync: string;
  alerts: string;
  carePlanAssigned?: boolean;
};
